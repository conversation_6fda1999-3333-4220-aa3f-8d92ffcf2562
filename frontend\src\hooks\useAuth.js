import { useSelector, useDispatch } from 'react-redux';
import { useEffect } from 'react';
import {
  useLoginMutation,
  useLogoutMutation,
  useValidateSessionQuery,
  useGetCurrentUserQuery,
} from '../store/api/authApi';
import {
  selectAuth,
  selectIsAuthenticated,
  selectUser,
  selectAuthLoading,
  selectAuthError,
  selectSessionChecked,
  clearError,
  resetAuth,
} from '../store/slices/authSlice';

// Hook personnalisé pour l'authentification
export const useAuth = () => {
  const dispatch = useDispatch();
  
  // Sélecteurs Redux
  const auth = useSelector(selectAuth);
  const isAuthenticated = useSelector(selectIsAuthenticated);
  const user = useSelector(selectUser);
  const isLoading = useSelector(selectAuthLoading);
  const error = useSelector(selectAuthError);
  const sessionChecked = useSelector(selectSessionChecked);

  // Mutations RTK Query
  const [loginMutation, { isLoading: isLoginLoading }] = useLoginMutation();
  const [logoutMutation, { isLoading: isLogoutLoading }] = useLogoutMutation();

  // Queries RTK Query
  const {
    data: sessionData,
    isLoading: isValidatingSession,
    refetch: refetchSession,
  } = useValidateSessionQuery(undefined, {
    skip: sessionChecked, // Skip si déjà vérifié
  });

  const {
    data: currentUserData,
    isLoading: isLoadingUser,
    refetch: refetchUser,
  } = useGetCurrentUserQuery(undefined, {
    skip: !isAuthenticated, // Skip si pas authentifié
  });

  // Fonction de login
  const login = async (credentials) => {
    try {
      const result = await loginMutation(credentials).unwrap();
      // Après login réussi, récupérer les infos utilisateur
      await refetchUser();
      return result;
    } catch (error) {
      throw error;
    }
  };

  // Fonction de logout
  const logout = async () => {
    try {
      await logoutMutation().unwrap();
      dispatch(resetAuth());
    } catch (error) {
      // Même en cas d'erreur, on déconnecte localement
      dispatch(resetAuth());
      throw error;
    }
  };

  // Fonction pour vérifier la session
  const checkSession = async () => {
    try {
      await refetchSession();
    } catch (error) {
      console.error('Session check failed:', error);
    }
  };

  // Fonction pour nettoyer les erreurs
  const clearAuthError = () => {
    dispatch(clearError());
  };

  // Fonction pour obtenir le chemin de redirection selon le rôle
  const getRedirectPath = (userRole) => {
    switch (userRole) {
      case 'CANDIDATE':
        return '/candidate/dashboard';
      case 'RH_ADMIN':
        return '/rh/dashboard';
      case 'PROJECT_LEADER':
        return '/project-leader/dashboard';
      case 'CEO':
        return '/ceo/dashboard';
      default:
        return '/';
    }
  };

  // Effet pour initialiser la session au démarrage
  useEffect(() => {
    if (!sessionChecked && !isValidatingSession) {
      checkSession();
    }
  }, [sessionChecked, isValidatingSession]);

  return {
    // État
    isAuthenticated,
    user: currentUserData || user,
    isLoading: isLoading || isLoginLoading || isLogoutLoading || isLoadingUser,
    error,
    sessionChecked,
    
    // Actions
    login,
    logout,
    checkSession,
    clearError: clearAuthError,
    getRedirectPath,
    
    // Utilitaires
    refetchUser,
    refetchSession,
  };
};
