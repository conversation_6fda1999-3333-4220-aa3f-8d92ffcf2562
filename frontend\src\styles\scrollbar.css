/* Custom Scrollbar Styles */

/* Webkit browsers (Chrome, Safari, Edge) */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

.custom-scrollbar::-webkit-scrollbar-thumb:active {
  background: #64748b;
}

/* Firefox */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

/* Thin scrollbar variant */
.custom-scrollbar-thin::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.custom-scrollbar-thin::-webkit-scrollbar-track {
  background: #f8fafc;
  border-radius: 3px;
}

.custom-scrollbar-thin::-webkit-scrollbar-thumb {
  background: #e2e8f0;
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

.custom-scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: #cbd5e1;
}

.custom-scrollbar-thin::-webkit-scrollbar-thumb:active {
  background: #94a3b8;
}

.custom-scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: #e2e8f0 #f8fafc;
}

/* Modal specific scrollbar */
.modal-scrollbar::-webkit-scrollbar {
  width: 12px;
}

.modal-scrollbar::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 6px;
  margin: 8px 0;
  border: 1px solid #e2e8f0;
}

.modal-scrollbar::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 6px;
  border: 2px solid #f1f5f9;
  transition: all 0.2s ease;
  min-height: 20px;
}

.modal-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
  border: 2px solid #e2e8f0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.modal-scrollbar::-webkit-scrollbar-thumb:active {
  background: #64748b;
}

.modal-scrollbar {
  scrollbar-width: auto;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

/* Force scrollbar to always be visible */
.modal-scrollbar {
  overflow-y: scroll !important;
}

/* Enhanced scrollbar for better visibility */
.modal-scrollbar::-webkit-scrollbar-corner {
  background: #f1f5f9;
}

/* Smooth scrolling */
.smooth-scroll {
  scroll-behavior: smooth;
}

/* Hide scrollbar but keep functionality */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}
