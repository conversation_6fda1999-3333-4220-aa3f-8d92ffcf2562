import apiClient from './apiClient';

const authApi = {
  // Login
  login: async (credentials) => {
    const response = await apiClient.post('/auth/login', credentials);
    return response.data;
  },

  // Logout
  logout: async () => {
    const response = await apiClient.post('/auth/logout');
    return response.data;
  },

  // Rafraîchir la session
  refresh: async () => {
    const response = await apiClient.post('/auth/refresh');
    return response.data;
  },

  // Obtenir les infos de l'utilisateur connecté (via user-service)
  getCurrentUser: async () => {
    const response = await apiClient.get('/profile/me');
    return response.data;
  },

  // Changer le mot de passe
  changePassword: async (passwordData) => {
    const response = await apiClient.post('/auth/change-password', passwordData);
    return response.data;
  },

  // Mot de passe oublié
  forgotPassword: async (email) => {
    const response = await apiClient.post('/auth/forgot-password/request', { email });
    return response.data;
  },

  // Réinitialiser le mot de passe
  resetPassword: async (resetData) => {
    const response = await apiClient.post('/auth/forgot-password/reset', resetData);
    return response.data;
  },

  // Valider le token (vérifier si l'utilisateur est connecté)
  validateSession: async () => {
    const response = await apiClient.post('/auth/validate');
    return response.data;
  },
};

export default authApi;