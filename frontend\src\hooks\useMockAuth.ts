// ============================================================================
// MOCK AUTH HOOK - For backward compatibility
// ============================================================================

import { useAuth } from '@/context/AuthContext';
import { authService } from '@/services/authService';

export interface CandidateRegistrationData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  phone?: string;
  resumeFile?: File | null;
  preferredJobCategories?: string[];
  educationLevel?: string;
  yearsOfExperience?: string;
  skills?: string[];
  linkedinProfile?: string;
  portfolioUrl?: string;
  coverLetter?: string;
  availabilityDate?: string;
  salaryExpectation?: string;
  willingToRelocate?: boolean;
  preferredWorkLocation?: string;
  languageSkills?: Array<{
    language: string;
    proficiency: string;
  }>;
  certifications?: string[];
  references?: Array<{
    name: string;
    position: string;
    company: string;
    email: string;
    phone: string;
  }>;
}

export const useMockAuth = () => {
  const auth = useAuth();

  const registerAsCandidate = async (data: CandidateRegistrationData) => {
    try {
      const user = await authService.register({
        email: data.email,
        password: data.password,
        firstName: data.firstName,
        lastName: data.lastName,
        role: 'CANDIDATE'
      });

      // Store user in localStorage (this is handled by AuthContext)
      localStorage.setItem('auth_user', JSON.stringify(user));
      
      return {
        success: true,
        user,
        message: 'Registration successful'
      };
    } catch (error) {
      throw error;
    }
  };

  return {
    ...auth,
    registerAsCandidate
  };
};
