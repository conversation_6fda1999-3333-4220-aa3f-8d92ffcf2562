<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="fcfaa31c-6c2d-44ce-a62f-cc48c6f4d41f" name="Changes" comment="">
      <change afterPath="$PROJECT_DIR$/Api-Gateway/src/main/java/com/PFE2025/Api_Gateway/config/RateLimitConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/Api-Gateway/src/main/java/com/PFE2025/Api_Gateway/config/ResilienceConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/Api-Gateway/src/main/java/com/PFE2025/Api_Gateway/filter/AuthenticationFilter.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/Api-Gateway/src/main/java/com/PFE2025/Api_Gateway/filter/CookiePropagationFilter.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/Api-Gateway/src/main/java/com/PFE2025/Api_Gateway/model/TokenValidationResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ai-processing-service/.gitattributes" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ai-processing-service/.gitignore" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ai-processing-service/.mvn/wrapper/maven-wrapper.properties" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ai-processing-service/mvnw" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ai-processing-service/mvnw.cmd" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ai-processing-service/pom.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ai-processing-service/src/main/java/com/pfe2025/ai_processing_service/AiProcessingServiceApplication.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ai-processing-service/src/main/java/com/pfe2025/ai_processing_service/config/RabbitMQStreamConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ai-processing-service/src/main/java/com/pfe2025/ai_processing_service/config/TogetherAiConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ai-processing-service/src/main/java/com/pfe2025/ai_processing_service/config/WebClientConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ai-processing-service/src/main/java/com/pfe2025/ai_processing_service/dto/CvParsedEventDto.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ai-processing-service/src/main/java/com/pfe2025/ai_processing_service/dto/DocumentEventDto.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ai-processing-service/src/main/java/com/pfe2025/ai_processing_service/dto/ProcessingErrorEventDto.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ai-processing-service/src/main/java/com/pfe2025/ai_processing_service/dto/TogetherAiRequest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ai-processing-service/src/main/java/com/pfe2025/ai_processing_service/dto/TogetherAiResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ai-processing-service/src/main/java/com/pfe2025/ai_processing_service/exception/AiApiException.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ai-processing-service/src/main/java/com/pfe2025/ai_processing_service/exception/DocumentFetchException.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ai-processing-service/src/main/java/com/pfe2025/ai_processing_service/exception/JsonParsingException.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ai-processing-service/src/main/java/com/pfe2025/ai_processing_service/exception/PdfParsingException.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ai-processing-service/src/main/java/com/pfe2025/ai_processing_service/service/AiProcessorService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ai-processing-service/src/main/java/com/pfe2025/ai_processing_service/service/LanguageDetectorService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ai-processing-service/src/main/java/com/pfe2025/ai_processing_service/service/PdfTextExtractor.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ai-processing-service/src/main/java/com/pfe2025/ai_processing_service/service/TogetherAiClient.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ai-processing-service/src/main/java/com/pfe2025/ai_processing_service/util/JsonParserUtil.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ai-processing-service/src/main/resources/application.yml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ai-processing-service/src/test/java/com/pfe2025/ai_processing_service/AiProcessingServiceApplicationTests.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/auth-service/src/main/java/com/PFE2025/auth_service/dto/ForgotPasswordRequest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/auth-service/src/main/java/com/PFE2025/auth_service/dto/LoginRequest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/auth-service/src/main/java/com/PFE2025/auth_service/dto/PasswordChangeRequest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/auth-service/src/main/java/com/PFE2025/auth_service/dto/ResetPasswordRequest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/auth-service/src/main/java/com/PFE2025/auth_service/dto/TokenResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/auth-service/src/main/java/com/PFE2025/auth_service/dto/TokenValidationResult.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/auth-service/src/main/java/com/PFE2025/auth_service/dto/UserCreateRequest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/auth-service/src/main/java/com/PFE2025/auth_service/dto/UserDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/auth-service/src/main/java/com/PFE2025/auth_service/exception/BaseException.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/auth-service/src/main/java/com/PFE2025/auth_service/exception/GlobalErrorHandler.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/auth-service/src/main/java/com/PFE2025/auth_service/exception/InvalidCredentialsException.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/auth-service/src/main/java/com/PFE2025/auth_service/exception/InvalidTokenException.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/auth-service/src/main/java/com/PFE2025/auth_service/exception/KeycloakException.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/auth-service/src/main/java/com/PFE2025/auth_service/exception/ResourceNotFoundException.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/auth-service/src/main/java/com/PFE2025/auth_service/exception/UserAlreadyExistsException.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/auth-service/src/main/java/com/PFE2025/auth_service/health/KeycloakHealthIndicator.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/auth-service/src/main/java/com/PFE2025/auth_service/health/RedisHealthIndicator.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/auth-service/src/main/java/com/PFE2025/auth_service/service/AuditService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/auth-service/src/main/java/com/PFE2025/auth_service/service/CookieService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/auth-service/src/main/java/com/PFE2025/auth_service/service/RateLimiterService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/document-management-service/.gitattributes" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/document-management-service/.gitignore" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/document-management-service/.mvn/wrapper/maven-wrapper.properties" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/document-management-service/mvnw" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/document-management-service/mvnw.cmd" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/document-management-service/pom.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/document-management-service/src/main/java/com/pfe2025/document_management_service/DocumentManagementServiceApplication.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/document-management-service/src/main/java/com/pfe2025/document_management_service/config/DatabaseConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/document-management-service/src/main/java/com/pfe2025/document_management_service/config/KeycloakRoleConverter.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/document-management-service/src/main/java/com/pfe2025/document_management_service/config/MetricsConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/document-management-service/src/main/java/com/pfe2025/document_management_service/config/MinioConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/document-management-service/src/main/java/com/pfe2025/document_management_service/config/OpenAPIConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/document-management-service/src/main/java/com/pfe2025/document_management_service/config/SecurityConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/document-management-service/src/main/java/com/pfe2025/document_management_service/controller/DocumentController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/document-management-service/src/main/java/com/pfe2025/document_management_service/dto/ApiResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/document-management-service/src/main/java/com/pfe2025/document_management_service/dto/DocumentDto.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/document-management-service/src/main/java/com/pfe2025/document_management_service/dto/DocumentEventDto.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/document-management-service/src/main/java/com/pfe2025/document_management_service/dto/DocumentUploadResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/document-management-service/src/main/java/com/pfe2025/document_management_service/dto/PresignedUrlRequest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/document-management-service/src/main/java/com/pfe2025/document_management_service/exception/DocumentNotFoundException.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/document-management-service/src/main/java/com/pfe2025/document_management_service/exception/EventPublishingException.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/document-management-service/src/main/java/com/pfe2025/document_management_service/exception/GlobalExceptionHandler.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/document-management-service/src/main/java/com/pfe2025/document_management_service/exception/InvalidFileException.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/document-management-service/src/main/java/com/pfe2025/document_management_service/exception/StorageException.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/document-management-service/src/main/java/com/pfe2025/document_management_service/model/Document.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/document-management-service/src/main/java/com/pfe2025/document_management_service/repository/DocumentRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/document-management-service/src/main/java/com/pfe2025/document_management_service/service/DocumentService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/document-management-service/src/main/java/com/pfe2025/document_management_service/service/FileSecurityService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/document-management-service/src/main/java/com/pfe2025/document_management_service/service/MinioService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/document-management-service/src/main/resources/application.yml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/document-management-service/src/test/java/com/pfe2025/document_management_service/DocumentManagementServiceApplicationTests.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/.gitattributes" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/.gitignore" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/.mvn/wrapper/maven-wrapper.properties" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/Dockerfile" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/mvnw" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/mvnw.cmd" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/pom.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/JobPostingServiceApplication.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/config/AIClientConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/config/AsyncConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/config/CacheConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/config/CronConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/config/JacksonConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/config/KeycloakRoleConverter.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/config/OpenAPIConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/config/RabbitMQConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/config/ResilienceConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/config/SecurityConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/controller/AIAssistanceController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/controller/ContentFragmentController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/controller/JobPostController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/controller/MetricsController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/controller/PublicJobPostController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/controller/TemplateController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/dto/AIAssistanceDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/dto/ActivityLogDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/dto/ContentFragmentDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/dto/JobPostDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/dto/JobPostingSkillDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/dto/MetricsDailySnapshotDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/dto/PageResponseDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/dto/PostingMetricsDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/dto/PostingTemplateDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/dto/SearchCriteriaDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/event/ApplicationSubmittedEvent.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/event/RequisitionApprovedEvent.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/event/RequisitionCancelledEvent.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/event/listener/ApplicationEventListener.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/event/listener/RequisitionEventListener.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/exception/AIServiceException.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/exception/AuthorizationException.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/exception/GlobalExceptionHandler.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/exception/InvalidOperationException.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/exception/ResourceNotFoundException.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/exception/ServiceIntegrationException.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/exception/StatusTransitionException.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/exception/ValidationException.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/integration/TogetherAIClient.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/mapper/ActivityLogMapper.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/mapper/ContentFragmentMapper.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/mapper/JobPostMapper.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/mapper/JobPostingSkillMapper.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/mapper/PostingMetricsMapper.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/mapper/PostingTemplateMapper.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/model/ActivityLog.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/model/BaseEntity.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/model/ContentFragment.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/model/ErrorResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/model/JobPost.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/model/JobPostingSkill.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/model/MetricsDailySnapshot.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/model/PostingMetrics.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/model/PostingTemplate.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/model/enums/EmploymentType.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/model/enums/PostingStatus.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/model/enums/PublicationLevel.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/repository/ActivityLogRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/repository/ContentFragmentRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/repository/JobPostRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/repository/JobPostingSkillRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/repository/MetricsDailySnapshotRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/repository/PostingMetricsRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/repository/PostingTemplateRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/repository/specification/ContentFragmentSpecifications.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/repository/specification/JobPostSpecifications.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/service/AIAssistanceService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/service/AuthenticationService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/service/ContentFragmentService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/service/JobPostService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/service/MetricsService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/service/TemplateService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/util/DateUtils.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/util/JsonUtils.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/util/MetricsCalculator.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/util/SecurityUtils.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/validator/ContentFragmentValidator.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/validator/JobPostValidator.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/java/com/pfe2025/jobpostingservice/validator/TemplateValidator.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/resources/application.yml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/resources/db/migration/V1__initial_schema.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/resources/db/migration/V2__add_sample_data.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/main/resources/db/migration/V3__add_indexes.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-posting-service/src/test/java/com/pfe2025/jobpostingservice/JobPostingServiceApplicationTests.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-requisition-service/.gitattributes" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-requisition-service/.gitignore" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-requisition-service/.mvn/wrapper/maven-wrapper.properties" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-requisition-service/Dockerfile" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-requisition-service/mvnw" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-requisition-service/mvnw.cmd" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-requisition-service/pom.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-requisition-service/src/main/java/com/pfe2025/jobrequisitionservice/JobRequisitionServiceApplication.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-requisition-service/src/main/java/com/pfe2025/jobrequisitionservice/config/KeycloakRoleConverter.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-requisition-service/src/main/java/com/pfe2025/jobrequisitionservice/config/OpenAPIConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-requisition-service/src/main/java/com/pfe2025/jobrequisitionservice/config/ResilienceConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-requisition-service/src/main/java/com/pfe2025/jobrequisitionservice/config/SecurityConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-requisition-service/src/main/java/com/pfe2025/jobrequisitionservice/config/StreamConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-requisition-service/src/main/java/com/pfe2025/jobrequisitionservice/contoller/JobRequisitionController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-requisition-service/src/main/java/com/pfe2025/jobrequisitionservice/dto/JobRequisitionDto.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-requisition-service/src/main/java/com/pfe2025/jobrequisitionservice/dto/JobRequisitionSummaryDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-requisition-service/src/main/java/com/pfe2025/jobrequisitionservice/dto/StatusHistoryResponseDto.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-requisition-service/src/main/java/com/pfe2025/jobrequisitionservice/dto/StatusUpdateDto.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-requisition-service/src/main/java/com/pfe2025/jobrequisitionservice/event/BaseEvent.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-requisition-service/src/main/java/com/pfe2025/jobrequisitionservice/event/RequisitionApprovedEvent.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-requisition-service/src/main/java/com/pfe2025/jobrequisitionservice/event/RequisitionCancelledEvent.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-requisition-service/src/main/java/com/pfe2025/jobrequisitionservice/event/RequisitionFulfilledEvent.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-requisition-service/src/main/java/com/pfe2025/jobrequisitionservice/event/RequisitionStatusChangedEvent.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-requisition-service/src/main/java/com/pfe2025/jobrequisitionservice/exception/GlobalExceptionHandler.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-requisition-service/src/main/java/com/pfe2025/jobrequisitionservice/exception/ResourceNotFoundException.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-requisition-service/src/main/java/com/pfe2025/jobrequisitionservice/exception/StatusTransitionException.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-requisition-service/src/main/java/com/pfe2025/jobrequisitionservice/exception/UnauthorizedException.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-requisition-service/src/main/java/com/pfe2025/jobrequisitionservice/mapper/JobRequisitionMapper.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-requisition-service/src/main/java/com/pfe2025/jobrequisitionservice/model/JobRequisition.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-requisition-service/src/main/java/com/pfe2025/jobrequisitionservice/repository/JobRequisitionRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-requisition-service/src/main/java/com/pfe2025/jobrequisitionservice/repository/RequisitionStatusHistoryRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-requisition-service/src/main/java/com/pfe2025/jobrequisitionservice/service/AuthenticationService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-requisition-service/src/main/java/com/pfe2025/jobrequisitionservice/service/JobRequisitionService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-requisition-service/src/main/java/com/pfe2025/jobrequisitionservice/service/JobRequisitionServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-requisition-service/src/main/java/com/pfe2025/jobrequisitionservice/service/MessagingService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-requisition-service/src/main/java/com/pfe2025/jobrequisitionservice/service/StatusTransitionService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-requisition-service/src/main/resources/application.yml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-requisition-service/src/main/resources/db/migration/V1__initial_schema.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/job-requisition-service/src/test/java/com/pfe2025/jobrequisitionservice/JobRequisitingServiceApplicationTests.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/user-service/src/main/java/com/PFE2025/user_service/config/CustomErrorDecoder.java" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Enum" />
        <option value="Python Script" />
        <option value="Interface" />
        <option value="Dockerfile" />
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
    <option name="ROOT_SYNC" value="DONT_SYNC" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$PROJECT_DIR$/Api-Gateway/src/main/java/com/PFE2025/Api_Gateway/filter/AuthPropagationFilter.java" root0="FORCE_HIGHLIGHTING" />
  </component>
  <component name="KubernetesApiProvider">{
  &quot;contexts&quot;: [
    {
      &quot;name&quot;: &quot;minikube&quot;,
      &quot;originalNamespace&quot;: &quot;default&quot;
    }
  ],
  &quot;isMigrated&quot;: true
}</component>
  <component name="KubernetesSettings">
    <option name="contextName" value="minikube" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="DEPENDENCY_CHECKER_PROBLEMS_TAB" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="2upgFXCFdQMUgM9mAjnDPkF8JAV" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;ASKED_ADD_EXTERNAL_FILES&quot;: &quot;true&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;WebServerToolWindowFactoryState&quot;: &quot;false&quot;,
    &quot;git-widget-placeholder&quot;: &quot;main&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/Users/<USER>/Downloads/claude-code-jetbrains-plugin-0.1.11-beta/claude-code-jetbrains-plugin/lib/claude-code-jetbrains-plugin-0.1.11-beta.jar&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Modules&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.2&quot;,
    &quot;run.code.analysis.last.selected.profile&quot;: &quot;pProject Default&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;advanced.settings&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;DatabaseDriversLRU&quot;: [
      &quot;postgresql&quot;,
      &quot;redis&quot;
    ]
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CreateClassDialog.RecentsKey">
      <recent name="com.PFE2025.auth_service.dto" />
      <recent name="com.PFE2025.userservice.exception" />
      <recent name="com.PFE2025.userservice.model" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\Desktop\vermeg-Recrutment-Platform\application-service\src\main\resources\db\migration" />
      <recent name="C:\Users\<USER>\Desktop\vermeg-Recrutment-Platform\ai-processing-service\app" />
      <recent name="C:\Users\<USER>\Desktop\vermeg-Recrutment-Platform\ai-processing-service\proto" />
      <recent name="C:\Users\<USER>\Desktop\vermeg-Recrutment-Platform\ai-processing-service\app\utils" />
      <recent name="C:\Users\<USER>\Desktop\vermeg-Recrutment-Platform\docker" />
    </key>
  </component>
  <component name="RunAnythingCache">
    <option name="myCommands">
      <command value="mvn compile" />
      <command value="mvn clean --builder" />
      <command value="mvn flyway:repair" />
      <command value="mvn flyway:clean" />
      <command value="mvn clean install" />
      <command value="mvn clean" />
      <command value="mvn clean package" />
    </option>
  </component>
  <component name="RunManager" selected="Spring Boot.DocumentManagementServiceApplication">
    <configuration name="V1__Initial_Schema.sql" type="DatabaseScript" editBeforeRun="true" temporary="true" nameIsGenerated="true">
      <script-file value="$PROJECT_DIR$/application-service/src/main/resources/db/V1__Initial_Schema.sql" />
      <script-mode>FILE</script-mode>
      <method v="2" />
    </configuration>
    <configuration name="V1__Initial_Schema.sql" type="DatabaseScript" editBeforeRun="true" temporary="true" nameIsGenerated="true">
      <script-file value="$PROJECT_DIR$/application-service/src/main/resources/db/V1__Initial_Schema.sql" />
      <script-mode>FILE</script-mode>
      <method v="2" />
    </configuration>
    <configuration name="AiProcessingServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ai-processing-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.pfe2025.ai_processing_service.AiProcessingServiceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="AiProcessingServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ai-processing-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.pfe2025.ai_processing_service.AiProcessingServiceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ApiGatewayApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="api-gateway" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.PFE2025.Api_Gateway.ApiGatewayApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ApiGatewayApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="api-gateway" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.PFE2025.Api_Gateway.ApiGatewayApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ApplicationServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="application-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.pfe2025.application_service.ApplicationServiceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ApplicationServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="application-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.pfe2025.application_service.ApplicationServiceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="AuthServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="auth-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.PFE2025.auth_service.AuthServiceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="AuthServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="auth-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.PFE2025.auth_service.AuthServiceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="DiscoveryServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="discovery-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.PFE2025.discovery_service.DiscoveryServiceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="DiscoveryServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="discovery-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.PFE2025.discovery_service.DiscoveryServiceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="DocumentManagementServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="document-management-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.pfe2025.document_management_service.DocumentManagementServiceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="DocumentManagementServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="document-management-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.pfe2025.document_management_service.DocumentManagementServiceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="DocumentManagementServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="document-management-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.pfe2025.document_management_service.DocumentManagementServiceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="InterviewServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="interview-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.pfe2025.interview_service.InterviewServiceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="InterviewServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="interview-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.pfe2025.interview_service.InterviewServiceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="JobPostingServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="job-posting-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.pfe2025.jobpostingservice.JobPostingServiceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="JobPostingServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="job-posting-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.pfe2025.jobpostingservice.JobPostingServiceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="JobRequisitionServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="job-requisition-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.pfe2025.jobrequisitionservice.JobRequisitionServiceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="JobRequisitionServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="job-requisition-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.pfe2025.jobrequisitionservice.JobRequisitionServiceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="UserServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="user-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.PFE2025.user_service.UserServiceApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.PFE2025.user_service.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="UserServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="user-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.PFE2025.user_service.UserServiceApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.PFE2025.user_service.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="UserServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="user-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.PFE2025.user_service.UserServiceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Spring Boot.UserServiceApplication" />
        <item itemvalue="Database Script.V1__Initial_Schema.sql" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="fcfaa31c-6c2d-44ce-a62f-cc48c6f4d41f" name="Changes" comment="" />
      <created>1742950827254</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1742950827254</updated>
      <workItem from="1742950828502" duration="267000" />
      <workItem from="1742951141443" duration="2958000" />
      <workItem from="1742954359749" duration="1035000" />
      <workItem from="1742957502604" duration="1081000" />
      <workItem from="1742959018387" duration="3070000" />
      <workItem from="1742963684401" duration="275000" />
      <workItem from="1742964001410" duration="37000" />
      <workItem from="1742964180090" duration="542000" />
      <workItem from="1742964915856" duration="1200000" />
      <workItem from="1742966331041" duration="47000" />
      <workItem from="1742966395479" duration="2317000" />
      <workItem from="1742968794348" duration="60000" />
      <workItem from="1742969229003" duration="677000" />
      <workItem from="1742970118351" duration="1418000" />
      <workItem from="1742971697875" duration="82000" />
      <workItem from="1742974256096" duration="6492000" />
      <workItem from="1742983588819" duration="59000" />
      <workItem from="1743019256401" duration="2039000" />
      <workItem from="1743021474110" duration="27968000" />
      <workItem from="1743125796202" duration="32546000" />
      <workItem from="1743239732280" duration="17399000" />
      <workItem from="1743320968725" duration="14209000" />
      <workItem from="1743476604107" duration="8991000" />
      <workItem from="1743580796661" duration="3640000" />
      <workItem from="1743940157848" duration="2154000" />
      <workItem from="1744098343833" duration="1339000" />
      <workItem from="1744134718691" duration="3726000" />
      <workItem from="1744205065404" duration="13536000" />
      <workItem from="1744276411873" duration="5416000" />
      <workItem from="1744299176558" duration="8814000" />
      <workItem from="1744370652458" duration="2607000" />
      <workItem from="1744375269296" duration="1762000" />
      <workItem from="1744377054071" duration="987000" />
      <workItem from="1744378062618" duration="10010000" />
      <workItem from="1744392763291" duration="5590000" />
      <workItem from="1744451983754" duration="4654000" />
      <workItem from="1744497223041" duration="9707000" />
      <workItem from="1744541725469" duration="22576000" />
      <workItem from="1744570606615" duration="8678000" />
      <workItem from="1744651765592" duration="21744000" />
      <workItem from="1744735060046" duration="8233000" />
      <workItem from="1744752390384" duration="609000" />
      <workItem from="1744994478998" duration="20466000" />
      <workItem from="1745099686055" duration="7580000" />
      <workItem from="1745109358945" duration="874000" />
      <workItem from="1745167228905" duration="1266000" />
      <workItem from="1745178447409" duration="3313000" />
      <workItem from="1745182325306" duration="24180000" />
      <workItem from="1745253644115" duration="14843000" />
      <workItem from="1745273395717" duration="17487000" />
      <workItem from="1745297075134" duration="5025000" />
      <workItem from="1745302175793" duration="64000" />
      <workItem from="1745337062063" duration="26550000" />
      <workItem from="1745393974664" duration="9649000" />
      <workItem from="1745448190359" duration="3080000" />
      <workItem from="1745458255241" duration="11293000" />
      <workItem from="1745475575189" duration="685000" />
      <workItem from="1745477189780" duration="5270000" />
      <workItem from="1745497878917" duration="605000" />
      <workItem from="1745658826945" duration="13944000" />
      <workItem from="1745675514125" duration="2656000" />
      <workItem from="1745746163170" duration="1262000" />
      <workItem from="1745840118383" duration="1947000" />
      <workItem from="1745848454509" duration="12117000" />
      <workItem from="1745914387912" duration="20188000" />
      <workItem from="1746008086772" duration="10275000" />
      <workItem from="1746088061594" duration="2261000" />
      <workItem from="1746094396776" duration="10703000" />
      <workItem from="1746144227690" duration="2822000" />
      <workItem from="1746185802333" duration="1214000" />
      <workItem from="1746306749311" duration="593000" />
      <workItem from="1746355451087" duration="7405000" />
      <workItem from="1746384808088" duration="7401000" />
      <workItem from="1746624889255" duration="3008000" />
      <workItem from="1746695754599" duration="341000" />
      <workItem from="1746699162261" duration="7289000" />
      <workItem from="1746706998956" duration="1780000" />
      <workItem from="1746716871929" duration="15019000" />
      <workItem from="1746879358607" duration="33685000" />
      <workItem from="1746995009686" duration="3544000" />
      <workItem from="1747055697315" duration="8032000" />
      <workItem from="1747082818639" duration="1458000" />
      <workItem from="1747130230933" duration="2862000" />
      <workItem from="1747429983677" duration="2420000" />
      <workItem from="1747473926228" duration="733000" />
      <workItem from="1747477686415" duration="586000" />
      <workItem from="1747495237423" duration="1336000" />
      <workItem from="1747572505632" duration="305000" />
      <workItem from="1747573665951" duration="1049000" />
      <workItem from="1747575585764" duration="743000" />
      <workItem from="1747614275021" duration="82000" />
      <workItem from="1747614401596" duration="487000" />
      <workItem from="1747644845109" duration="6105000" />
      <workItem from="1747824494545" duration="5135000" />
      <workItem from="1748253355666" duration="12830000" />
      <workItem from="1748423157270" duration="9485000" />
      <workItem from="1748455848205" duration="10057000" />
      <workItem from="1748531007380" duration="9445000" />
      <workItem from="1748629178809" duration="714000" />
      <workItem from="1748634059486" duration="230000" />
      <workItem from="1748795066424" duration="1438000" />
      <workItem from="1748798746304" duration="8387000" />
      <workItem from="1750615041192" duration="743000" />
      <workItem from="1751493736399" duration="3341000" />
      <workItem from="1751588407047" duration="9065000" />
      <workItem from="1751674752402" duration="961000" />
      <workItem from="1751675757564" duration="214000" />
      <workItem from="1751676128038" duration="4924000" />
      <workItem from="1751681139472" duration="1859000" />
      <workItem from="1751716417856" duration="1653000" />
      <workItem from="1751802084452" duration="13734000" />
      <workItem from="1751819205648" duration="21742000" />
      <workItem from="1751884138701" duration="4750000" />
      <workItem from="1751892621417" duration="158000" />
      <workItem from="1751892845744" duration="11721000" />
      <workItem from="1751973499432" duration="13656000" />
      <workItem from="1751997963904" duration="6609000" />
      <workItem from="1752016437128" duration="3022000" />
      <workItem from="1752097258759" duration="26000" />
      <workItem from="1752098252772" duration="3038000" />
      <workItem from="1752152880692" duration="2314000" />
      <workItem from="1752187892711" duration="2573000" />
      <workItem from="1752239932860" duration="3668000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <breakpoint enabled="true" type="java-exception">
          <properties class="org.springframework.web.method.annotation.MethodArgumentTypeMismatchException" package="org.springframework.web.method.annotation" />
          <option name="timeStamp" value="7" />
        </breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>