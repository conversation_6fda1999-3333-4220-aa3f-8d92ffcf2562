import { configureStore } from '@reduxjs/toolkit';
import authReducer from './authSlice';
import userReducer from './userSlice';

// Configuration du store Redux
export const store = configureStore({
  reducer: {
    auth: authReducer,
    user: userReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        // Ignorer les warnings pour les dates
        ignoredActions: ['auth/login/fulfilled'],
        ignoredPaths: ['auth.loginTime'],
      },
    }),
  devTools: process.env.NODE_ENV !== 'production',
});

export default store;