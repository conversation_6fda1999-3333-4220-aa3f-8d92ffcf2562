{"root": ["./src/app.tsx", "./src/main.tsx", "./src/vite-env.d.ts", "./src/assets/theme/modern-colors.ts", "./src/assets/theme/mui-theme.ts", "./src/components/auth/candidateregistrationform.tsx", "./src/components/auth/projectleaderregistrationform.tsx", "./src/components/auth/rhregistrationform.tsx", "./src/components/candidate/assessment.tsx", "./src/components/candidate/candidateprofile.tsx", "./src/components/candidate/candidateprofileedit.tsx", "./src/components/candidate/candidateprofilemanager.tsx", "./src/components/candidate/candidateprofileview.tsx", "./src/components/candidate/candidatesettings.tsx", "./src/components/candidate/jobsearch.tsx", "./src/components/candidate/myapplications.tsx", "./src/components/candidate/index.ts", "./src/components/ceo/approvalsqueue.tsx", "./src/components/ceo/enhancedceoapproval.tsx", "./src/components/ceo/enhancedceodashboard.tsx", "./src/components/ceo/jobapprovalsystem.tsx", "./src/components/ceo/jobsmanagement.tsx", "./src/components/ceo/systemanalyticsdashboard.tsx", "./src/components/ceo/usermanagementinterface.tsx", "./src/components/chatbot/chatbot.tsx", "./src/components/common/alert.tsx", "./src/components/common/badge.tsx", "./src/components/common/button.tsx", "./src/components/common/card.tsx", "./src/components/common/enhancedbadge.tsx", "./src/components/common/enhancedbutton.tsx", "./src/components/common/errorboundary.tsx", "./src/components/common/errordisplay.tsx", "./src/components/common/fileupload.tsx", "./src/components/common/input.tsx", "./src/components/common/loadingspinner.tsx", "./src/components/common/modal.tsx", "./src/components/common/muiwrapper.tsx", "./src/components/common/multiselect.tsx", "./src/components/common/notificationcontainer.tsx", "./src/components/common/profilephotoupload.tsx", "./src/components/common/registrationerrordisplay.tsx", "./src/components/common/registrationsuccessdisplay.tsx", "./src/components/common/select.tsx", "./src/components/common/textarea.tsx", "./src/components/common/toast.tsx", "./src/components/common/index.ts", "./src/components/debug/userdatadebug.tsx", "./src/components/dev/developmentstatus.tsx", "./src/components/dev/frontendonlynotification.tsx", "./src/components/jobs/careerssection.tsx", "./src/components/jobs/jobcard.tsx", "./src/components/jobs/jobfilters.tsx", "./src/components/landing/aboutsection.tsx", "./src/components/landing/footer.tsx", "./src/components/landing/hero.tsx", "./src/components/landing/jobslist.tsx", "./src/components/landing/mobilemenu.tsx", "./src/components/landing/navigation.tsx", "./src/components/landing/processsteps.tsx", "./src/components/landing/index.ts", "./src/components/modals/aireviewmodal.tsx", "./src/components/modals/applicationmodal.tsx", "./src/components/modals/candidatemodal.tsx", "./src/components/modals/internalmodal.tsx", "./src/components/modals/jobdetailmodal.tsx", "./src/components/modals/index.ts", "./src/components/navigation/dynamicnavbar.tsx", "./src/components/navigation/sectionnavigator.tsx", "./src/components/project-leader/accountsettings.tsx", "./src/components/project-leader/analytics.tsx", "./src/components/project-leader/applicationreview.tsx", "./src/components/project-leader/createjob.tsx", "./src/components/project-leader/enhancedapplicationreview.tsx", "./src/components/project-leader/enhancedcreatejob.tsx", "./src/components/project-leader/enhancedmyjobs.tsx", "./src/components/project-leader/helpsupport.tsx", "./src/components/project-leader/jobmanagement.tsx", "./src/components/project-leader/jobtemplates.tsx", "./src/components/project-leader/projectleaderprofile.tsx", "./src/components/project-leader/projectleaderprofilemanager.tsx", "./src/components/project-leader/quizbuilder.tsx", "./src/components/project-leader/teamanalytics.tsx", "./src/components/project-leader/teammanagement.tsx", "./src/components/public/enhancedjobapplication.tsx", "./src/components/public/enhancedjoblistings.tsx", "./src/components/rh/behavioralassessmentlibrary.tsx", "./src/components/rh/candidatemanagement.tsx", "./src/components/rh/cleanquizbuilder.tsx", "./src/components/rh/enhancedhrquizbuilder.tsx", "./src/components/rh/enhancedjobenhancement.tsx", "./src/components/rh/hranalytics.tsx", "./src/components/rh/hrprofilemanagement.tsx", "./src/components/rh/hrquizbuilder.tsx", "./src/components/rh/interviewcalendar.tsx", "./src/components/rh/interviewmanagement.tsx", "./src/components/rh/interviewschedulingmodal.tsx", "./src/components/rh/messagingcenter.tsx", "./src/components/rh/modernquizbuilder.tsx", "./src/components/rh/pipelinemanagementdashboard.tsx", "./src/components/rh/quizeditor.tsx", "./src/components/rh/quizmanagement.tsx", "./src/components/settings/accountsettings.tsx", "./src/context/authcontext.tsx", "./src/context/themeprovider.tsx", "./src/data/constants.ts", "./src/data/countrycodes.ts", "./src/data/dummydata.ts", "./src/data/job.ts", "./src/hooks/usemockauth.ts", "./src/hooks/usenotifications.ts", "./src/layouts/condidatelayout.tsx", "./src/layouts/dashboardlayout.tsx", "./src/layouts/minimallayout.tsx", "./src/layouts/index.ts", "./src/pages/candidate/candidateroutes.tsx", "./src/pages/candidate/dashboard.tsx", "./src/pages/candidate/jobsearchpage.tsx", "./src/pages/candidate/messagingpage.tsx", "./src/pages/candidate/myapplicationspage.tsx", "./src/pages/candidate/profiledemo.tsx", "./src/pages/ceo/approvalspage.tsx", "./src/pages/ceo/ceoroutes.tsx", "./src/pages/ceo/dashboard.tsx", "./src/pages/hr-admin/dashboard.tsx", "./src/pages/project-leader/createjobpage.tsx", "./src/pages/project-leader/dashboard.tsx", "./src/pages/project-leader/myjobspage.tsx", "./src/pages/project-leader/projectleaderroutes.tsx", "./src/pages/public/environmentinfo.tsx", "./src/pages/public/errorhandlingdemo.tsx", "./src/pages/public/homepage.tsx", "./src/pages/public/jobapplicationpage.tsx", "./src/pages/public/jobspage.tsx", "./src/pages/public/navbardemo.tsx", "./src/pages/public/notfoundpage.tsx", "./src/pages/public/processpage.tsx", "./src/pages/public/registrationpage.tsx", "./src/pages/public/signinpage.tsx", "./src/pages/public/unauthorizedpage.tsx", "./src/pages/public/index.ts", "./src/pages/rh/calendarpage.tsx", "./src/pages/rh/enhancejobpage.tsx", "./src/pages/rh/interviewmanagementpage.tsx", "./src/pages/rh/messagingpage.tsx", "./src/pages/rh/rhapplicationspage.tsx", "./src/pages/rh/rhquizpage.tsx", "./src/pages/rh/rhroutes.tsx", "./src/pages/super-admin/dashboard.tsx", "./src/routes/candidateroutes.tsx", "./src/routes/ceoroutes.tsx", "./src/routes/hradminroutes.tsx", "./src/routes/projectleaderroutes.tsx", "./src/routes/protectedroute.tsx", "./src/routes/superadminroutes.tsx", "./src/routes/index.ts", "./src/services/aireviewservice.ts", "./src/services/authservice.ts", "./src/services/candidateservice.ts", "./src/services/errorhandlingservice.ts", "./src/services/index.ts", "./src/services/interviewservice.ts", "./src/services/messagingservice.ts", "./src/services/mockworkflowservice.ts", "./src/store/index.ts", "./src/types/api.ts", "./src/types/auth.ts", "./src/types/candidateprofile.ts", "./src/types/common.ts", "./src/types/index.ts", "./src/types/job.ts", "./src/types/navigation.ts", "./src/types/user.ts", "./src/types/workflow.ts", "./src/utils/authredirect.ts", "./src/utils/cn.ts", "./src/utils/errormessages.ts"], "errors": true, "version": "5.8.3"}