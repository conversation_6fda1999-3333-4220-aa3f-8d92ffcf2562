import { configureStore } from '@reduxjs/toolkit';
import { persistStore, persistReducer } from 'redux-persist';
import storage from 'redux-persist/lib/storage';
import { combineReducers } from '@reduxjs/toolkit';
import { authApi } from './api/authApi';
import { userApi } from './api/userApi';
import authReducer from './slices/authSlice';

// Configuration de Redux Persist pour l'auth seulement
const authPersistConfig = {
  key: 'auth',
  storage,
  // Pas de whitelist/blacklist ici car on persiste tout l'authSlice
};

// Persister seulement le slice auth
const persistedAuthReducer = persistReducer(authPersistConfig, authReducer);

// Combiner les reducers
const rootReducer = combineReducers({
  auth: persistedAuthReducer, // Seul l'auth est persisté
  [authApi.reducerPath]: authApi.reducer, // APIs ne sont pas persistées
  [userApi.reducerPath]: userApi.reducer,
});

// Configuration du store Redux
export const store = configureStore({
  reducer: rootReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [
          'persist/PERSIST',
          'persist/REHYDRATE',
          'persist/PAUSE',
          'persist/PURGE',
          'persist/REGISTER',
          'persist/FLUSH',
        ],
        ignoredActionsPaths: ['meta.arg', 'payload.timestamp'],
        ignoredPaths: ['items.dates'],
      },
    })
      .concat(authApi.middleware)
      .concat(userApi.middleware),
  devTools: process.env.NODE_ENV !== 'production',
});

// Créer le persistor
export const persistor = persistStore(store);

// Types pour TypeScript
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

export default store;
