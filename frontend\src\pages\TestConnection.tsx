import React, { useState } from 'react';
import { useAuth } from '../hooks/useAuth';
import { useCandidate } from '../hooks/useCandidate';
import { useGetCurrentUserProfileQuery } from '../store/api/userApi';
import { toast } from 'react-toastify';

const TestConnection: React.FC = () => {
  const { login, logout, isAuthenticated, user, isLoading, error } = useAuth();
  const { registerCandidate, isRegistering } = useCandidate();
  const { data: profile, refetch: refetchProfile } = useGetCurrentUserProfileQuery(undefined, {
    skip: !isAuthenticated,
  });

  const [loginForm, setLoginForm] = useState({
    email: '',
    password: '',
  });

  const [testResults, setTestResults] = useState<string[]>([]);

  const addTestResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  // Test 1: Connexion
  const testLogin = async () => {
    try {
      addTestResult('🔄 Test de connexion...');
      await login(loginForm);
      addTestResult('✅ Connexion réussie !');
      toast.success('Connexion réussie !');
    } catch (error: any) {
      addTestResult(`❌ Erreur de connexion: ${error?.data?.message || error.message}`);
      toast.error('Erreur de connexion');
    }
  };

  // Test 2: Récupération du profil
  const testProfile = async () => {
    try {
      addTestResult('🔄 Test de récupération du profil...');
      await refetchProfile();
      addTestResult('✅ Profil récupéré avec succès !');
      toast.success('Profil récupéré !');
    } catch (error: any) {
      addTestResult(`❌ Erreur profil: ${error?.message}`);
      toast.error('Erreur lors de la récupération du profil');
    }
  };

  // Test 3: Déconnexion
  const testLogout = async () => {
    try {
      addTestResult('🔄 Test de déconnexion...');
      await logout();
      addTestResult('✅ Déconnexion réussie !');
      toast.success('Déconnexion réussie !');
    } catch (error: any) {
      addTestResult(`❌ Erreur déconnexion: ${error?.message}`);
      toast.error('Erreur de déconnexion');
    }
  };

  // Test 4: Test de connectivité backend
  const testBackendConnectivity = async () => {
    try {
      addTestResult('🔄 Test de connectivité backend...');
      const response = await fetch('http://localhost:7000/api/auth/health', {
        method: 'GET',
        credentials: 'include',
      });
      
      if (response.ok) {
        addTestResult('✅ Backend accessible !');
        toast.success('Backend accessible !');
      } else {
        addTestResult(`❌ Backend non accessible: ${response.status}`);
        toast.error('Backend non accessible');
      }
    } catch (error: any) {
      addTestResult(`❌ Erreur connectivité: ${error.message}`);
      toast.error('Erreur de connectivité');
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <h1 className="text-3xl font-bold text-center mb-8">Test de Connexion Backend</h1>

      {/* État actuel */}
      <div className="bg-blue-50 p-4 rounded-lg">
        <h2 className="text-xl font-semibold mb-2">État Actuel</h2>
        <div className="space-y-2">
          <p><strong>Authentifié:</strong> {isAuthenticated ? '✅ Oui' : '❌ Non'}</p>
          <p><strong>Chargement:</strong> {isLoading ? '🔄 Oui' : '✅ Non'}</p>
          <p><strong>Erreur:</strong> {error || 'Aucune'}</p>
          <p><strong>Utilisateur:</strong> {user?.email || 'Aucun'}</p>
          <p><strong>Rôles:</strong> {user?.roles?.join(', ') || 'Aucun'}</p>
        </div>
      </div>

      {/* Formulaire de connexion */}
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h2 className="text-xl font-semibold mb-4">Test de Connexion</h2>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Email
            </label>
            <input
              type="email"
              value={loginForm.email}
              onChange={(e) => setLoginForm(prev => ({ ...prev, email: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Entrez un email de test"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Mot de passe
            </label>
            <input
              type="password"
              value={loginForm.password}
              onChange={(e) => setLoginForm(prev => ({ ...prev, password: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Entrez le mot de passe"
            />
          </div>
        </div>
      </div>

      {/* Boutons de test */}
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h2 className="text-xl font-semibold mb-4">Tests Disponibles</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <button
            onClick={testBackendConnectivity}
            className="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700"
          >
            Test Connectivité
          </button>
          <button
            onClick={testLogin}
            disabled={isLoading || !loginForm.email || !loginForm.password}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50"
          >
            Test Login
          </button>
          <button
            onClick={testProfile}
            disabled={!isAuthenticated}
            className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 disabled:opacity-50"
          >
            Test Profil
          </button>
          <button
            onClick={testLogout}
            disabled={!isAuthenticated}
            className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 disabled:opacity-50"
          >
            Test Logout
          </button>
        </div>
      </div>

      {/* Profil utilisateur */}
      {profile && (
        <div className="bg-green-50 p-4 rounded-lg">
          <h2 className="text-xl font-semibold mb-2">Profil Utilisateur</h2>
          <pre className="text-sm bg-white p-3 rounded border overflow-auto">
            {JSON.stringify(profile, null, 2)}
          </pre>
        </div>
      )}

      {/* Résultats des tests */}
      <div className="bg-gray-50 p-4 rounded-lg">
        <div className="flex justify-between items-center mb-2">
          <h2 className="text-xl font-semibold">Résultats des Tests</h2>
          <button
            onClick={clearResults}
            className="bg-gray-600 text-white px-3 py-1 rounded-md hover:bg-gray-700 text-sm"
          >
            Effacer
          </button>
        </div>
        <div className="bg-black text-green-400 p-3 rounded font-mono text-sm max-h-64 overflow-auto">
          {testResults.length === 0 ? (
            <p>Aucun test exécuté...</p>
          ) : (
            testResults.map((result, index) => (
              <div key={index}>{result}</div>
            ))
          )}
        </div>
      </div>

      {/* Instructions */}
      <div className="bg-yellow-50 p-4 rounded-lg">
        <h2 className="text-xl font-semibold mb-2">Instructions</h2>
        <ol className="list-decimal list-inside space-y-1 text-sm">
          <li>Commencez par tester la connectivité backend</li>
          <li>Entrez les identifiants d'un utilisateur existant</li>
          <li>Testez la connexion</li>
          <li>Si connecté, testez la récupération du profil</li>
          <li>Testez la déconnexion</li>
        </ol>
      </div>
    </div>
  );
};

export default TestConnection;
