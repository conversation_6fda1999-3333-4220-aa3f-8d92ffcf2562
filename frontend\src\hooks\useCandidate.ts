import { useRegisterCandidateMutation } from '../store/api/userApi';
import { CandidateRegistrationData } from '../types/auth';

// Interface pour les données d'inscription candidat
interface CandidateRegistrationFormData {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  phone: string;
  location?: string;
  linkedinUrl?: string;
  portfolioUrl?: string;
  dateOfBirth?: string;
  preferredCategories?: string[];
  cvFile: File;
}

// Interface pour le retour du hook
interface UseCandidateReturn {
  registerCandidate: (candidateData: CandidateRegistrationFormData) => Promise<any>;
  isRegistering: boolean;
  registerError: any;
}

// Hook personnalisé pour les opérations candidat
export const useCandidate = (): UseCandidateReturn => {
  const [registerCandidateMutation, { isLoading: isRegistering, error: registerError }] = useRegisterCandidateMutation();

  // Fonction d'inscription candidat
  const registerCandidate = async (candidateData: CandidateRegistrationFormData) => {
    try {
      // Créer FormData pour l'upload de fichier
      const formData = new FormData();
      
      // Ajouter tous les champs au FormData
      formData.append('firstName', candidateData.firstName);
      formData.append('lastName', candidateData.lastName);
      formData.append('email', candidateData.email);
      formData.append('password', candidateData.password);
      formData.append('phone', candidateData.phone);
      
      // Champs optionnels
      if (candidateData.location) {
        formData.append('location', candidateData.location);
      }
      if (candidateData.linkedinUrl) {
        formData.append('linkedinUrl', candidateData.linkedinUrl);
      }
      if (candidateData.portfolioUrl) {
        formData.append('portfolioUrl', candidateData.portfolioUrl);
      }
      if (candidateData.dateOfBirth) {
        formData.append('dateOfBirth', candidateData.dateOfBirth);
      }
      
      // Gérer les catégories préférées
      if (candidateData.preferredCategories && candidateData.preferredCategories.length > 0) {
        formData.append('preferredCategories', candidateData.preferredCategories.join(','));
      }
      
      // Ajouter le fichier CV
      if (candidateData.cvFile) {
        formData.append('cvFile', candidateData.cvFile);
      }

      const result = await registerCandidateMutation(formData).unwrap();
      return result;
    } catch (error) {
      throw error;
    }
  };

  return {
    registerCandidate,
    isRegistering,
    registerError,
  };
};

// Export du type pour utilisation externe
export type { CandidateRegistrationFormData };
