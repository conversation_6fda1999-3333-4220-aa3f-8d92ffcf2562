import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';

// Configuration de base pour les appels API
const baseQuery = fetchBaseQuery({
  baseUrl: 'http://localhost:7000/api',
  credentials: 'include', // Important pour les cookies
  prepareHeaders: (headers, { getState }) => {
    // Pour les uploads de fichiers, ne pas définir Content-Type
    // Axios le fera automatiquement pour FormData
    return headers;
  },
});

// API des utilisateurs avec RTK Query
export const userApi = createApi({
  reducerPath: 'userApi',
  baseQuery,
  tagTypes: ['Profile', 'Candidate'],
  endpoints: (builder) => ({
    // Inscription candidat - POST /users/register
    registerCandidate: builder.mutation({
      query: (formData) => ({
        url: '/users/register',
        method: 'POST',
        body: formData, // FormData pour l'upload de fichier
        formData: true,
      }),
      invalidatesTags: ['Candidate'],
    }),

    // Obtenir le profil de l'utilisateur connecté - GET /profile/me
    getCurrentUserProfile: builder.query({
      query: () => '/profile/me',
      providesTags: ['Profile'],
    }),

    // Mettre à jour le profil de l'utilisateur connecté - PUT /profile/me
    updateCurrentUserProfile: builder.mutation({
      query: (profileData) => ({
        url: '/profile/me',
        method: 'PUT',
        body: profileData,
      }),
      invalidatesTags: ['Profile'],
    }),

    // Obtenir un candidat par ID Keycloak - GET /profile/candidate/{keycloakId}
    getCandidateProfile: builder.query({
      query: (keycloakId) => `/profile/candidate/${keycloakId}`,
      providesTags: (result, error, keycloakId) => [
        { type: 'Candidate', id: keycloakId },
      ],
    }),

    // Mettre à jour la photo de profil - PUT /profile/photo
    updateProfilePhoto: builder.mutation({
      query: (formData) => ({
        url: '/profile/photo',
        method: 'PUT',
        body: formData,
        formData: true,
      }),
      invalidatesTags: ['Profile'],
    }),

    // Obtenir tous les candidats (pour les admins) - GET /candidates
    getAllCandidates: builder.query({
      query: (params = {}) => ({
        url: '/candidates',
        params,
      }),
      providesTags: ['Candidate'],
    }),

    // Compter les profils candidats - GET /profile/candidates/count
    getCandidatesCount: builder.query({
      query: () => '/profile/candidates/count',
      providesTags: ['Candidate'],
    }),
  }),
});

// Export des hooks générés automatiquement
export const {
  useRegisterCandidateMutation,
  useGetCurrentUserProfileQuery,
  useUpdateCurrentUserProfileMutation,
  useGetCandidateProfileQuery,
  useUpdateProfilePhotoMutation,
  useGetAllCandidatesQuery,
  useGetCandidatesCountQuery,
} = userApi;
