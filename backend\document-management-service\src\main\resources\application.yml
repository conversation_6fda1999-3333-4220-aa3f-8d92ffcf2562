server:
  port: 7010

spring:
  application:
    name: document-management-service

  # Database Configuration
  r2dbc:
    url: r2dbc:postgresql://localhost:5432/vermeg_document
    username: ${DB_USERNAME:postgres}
    password: ${DB_PASSWORD:postgres}
    pool:
      enabled: true
      initial-size: 5
      max-size: 20
      max-idle-time: 30m
      validation-query: SELECT 1

  # Security Configuration
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: ${KEYCLOAK_URL:http://localhost:9090}/realms/vermeg-Platform

  # RabbitMQ Configuration
  rabbitmq:
    host: ${RABBITMQ_HOST:localhost}
    port: ${RABBITMQ_PORT:5672}
    username: ${RABBITMQ_USERNAME:guest}
    password: ${RABBITMQ_PASSWORD:guest}
    virtual-host: ${RABBITMQ_VHOST:/}

  cloud:
    stream:
      default-binder: rabbit
      rabbit:
        binder:
          admin-addresses: ${RABBITMQ_ADMIN_URL:http://localhost:15672}
        bindings:
          documentUploaded-out-0:
            producer:
              routing-key-expression: '''document.cv.uploaded'''
              declare-exchange: true
              auto-bind-dlq: true
      bindings:
        documentUploaded-out-0:
          destination: document-events
          content-type: application/json
          producer:
            required-groups: document-processing

# MinIO Configuration
minio:
  endpoint: ${MINIO_ENDPOINT:http://localhost:9000}
  access-key: ${MINIO_ACCESS_KEY:minioadmin}
  secret-key: ${MINIO_SECRET_KEY:minioadmin}
  bucket: ${MINIO_BUCKET:recruitment-documents}

# Document Configuration
document:
  upload:
    max-file-size: ********  # 10MB in bytes
    allowed-image-types: image/jpeg,image/png,image/webp
    allowed-document-types: application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document
  profile-photo:
    max-size: 5242880  # 5MB in bytes
    max-width: 1000
    max-height: 1000

# Eureka Configuration
eureka:
  client:
    serviceUrl:
      defaultZone: ${EUREKA_URI:http://localhost:8761/eureka}
    register-with-eureka: true
    fetch-registry: true
  instance:
    prefer-ip-address: true

# Actuator Configuration
management:
  endpoints:
    web:
      exposure:
        include: health,info,prometheus,metrics
  endpoint:
    health:
      show-details: when_authorized
      probes:
        enabled: true

# Logging Configuration
logging:
  level:
    com.PFE2025: DEBUG
    org.springframework.security: INFO
    io.minio: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
# OpenAPI/Swagger Configuration

# --- Configuration Springdoc / OpenAPI (Documentation API) ---
springdoc:
  api-docs:
    enabled: true
    path: /v3/api-docs
  swagger-ui:
    enabled: true
    path: /swagger-ui.html
    tagsSorter: alpha
    operationsSorter: method
    # Disable deep linking to avoid webjars paths
    deepLinking: false
    # Configure UI without webjars references
    displayRequestDuration: true
    displayOperationId: false
  show-actuator: false
  packages-to-scan: com.pfe2025.document_management_service.controller
  # Ensure no webjars resources are loaded
  use-management-port: false

