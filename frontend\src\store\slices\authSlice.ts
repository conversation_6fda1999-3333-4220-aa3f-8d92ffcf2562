import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { authApi } from '../api/authApi';
import { TokenValidationResult } from '../../types/auth';
import type { RootState } from '../store';

// Interface pour l'état d'authentification
interface AuthState {
  isAuthenticated: boolean;
  user: TokenValidationResult | null;
  isLoading: boolean;
  error: string | null;
  sessionChecked: boolean;
}

// État initial
const initialState: AuthState = {
  isAuthenticated: false,
  user: null,
  isLoading: false,
  error: null,
  sessionChecked: false,
};

// Slice d'authentification
const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    // Actions synchrones
    clearError: (state) => {
      state.error = null;
    },
    setSessionChecked: (state, action: PayloadAction<boolean>) => {
      state.sessionChecked = action.payload;
    },
    resetAuth: (state) => {
      state.isAuthenticated = false;
      state.user = null;
      state.error = null;
      state.sessionChecked = false;
    },
    setUser: (state, action: PayloadAction<TokenValidationResult>) => {
      state.user = action.payload;
      state.isAuthenticated = true;
    },
  },
  extraReducers: (builder) => {
    // Login
    builder
      .addMatcher(authApi.endpoints.login.matchPending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addMatcher(authApi.endpoints.login.matchFulfilled, (state) => {
        state.isLoading = false;
        state.isAuthenticated = true;
        state.error = null;
        state.sessionChecked = true;
        // Les données utilisateur seront récupérées séparément
      })
      .addMatcher(authApi.endpoints.login.matchRejected, (state, action) => {
        state.isLoading = false;
        state.isAuthenticated = false;
        state.error = action.error?.message || 'Login failed';
      });

    // Logout
    builder
      .addMatcher(authApi.endpoints.logout.matchPending, (state) => {
        state.isLoading = true;
      })
      .addMatcher(authApi.endpoints.logout.matchFulfilled, (state) => {
        state.isLoading = false;
        state.isAuthenticated = false;
        state.user = null;
        state.error = null;
        state.sessionChecked = false;
      })
      .addMatcher(authApi.endpoints.logout.matchRejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error?.message || 'Logout failed';
      });

    // Validation de session
    builder
      .addMatcher(authApi.endpoints.validateSession.matchPending, (state) => {
        state.isLoading = true;
      })
      .addMatcher(authApi.endpoints.validateSession.matchFulfilled, (state, action) => {
        console.log('🔄 Redux: validateSession fulfilled with payload:', action.payload);
        state.isLoading = false;
        state.sessionChecked = true;
        if (action.payload?.valid) {
          console.log('✅ Redux: Setting user as authenticated:', action.payload);
          state.isAuthenticated = true;
          state.user = action.payload;
          state.error = null;
        } else {
          console.log('❌ Redux: Session not valid, clearing user');
          state.isAuthenticated = false;
          state.user = null;
        }
        console.log('🔍 Redux: New auth state:', {
          isAuthenticated: state.isAuthenticated,
          user: state.user?.username || 'null'
        });
      })
      .addMatcher(authApi.endpoints.validateSession.matchRejected, (state) => {
        state.isLoading = false;
        state.isAuthenticated = false;
        state.user = null;
        state.sessionChecked = true;
      });

    // Obtenir l'utilisateur connecté
    builder
      .addMatcher(authApi.endpoints.getCurrentUser.matchFulfilled, (state, action) => {
        if (action.payload?.valid) {
          state.user = action.payload;
          state.isAuthenticated = true;
        }
      })
      .addMatcher(authApi.endpoints.getCurrentUser.matchRejected, (state) => {
        state.user = null;
        state.isAuthenticated = false;
      });
  },
});

// Export des actions
export const { clearError, setSessionChecked, resetAuth, setUser } = authSlice.actions;

// Sélecteurs
export const selectAuth = (state: RootState) => state.auth;
export const selectIsAuthenticated = (state: RootState) => state.auth.isAuthenticated;
export const selectUser = (state: RootState) => state.auth.user;
export const selectAuthLoading = (state: RootState) => state.auth.isLoading;
export const selectAuthError = (state: RootState) => state.auth.error;
export const selectSessionChecked = (state: RootState) => state.auth.sessionChecked;

export default authSlice.reducer;
