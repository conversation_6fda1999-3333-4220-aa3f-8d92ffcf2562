{"name": "vermeg-recruitment-platform", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "lint:fix": "eslint src --ext ts,tsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx,css,md}\"", "type-check": "tsc --noEmit"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hookform/resolvers": "^5.0.1", "@mui/icons-material": "^7.1.0", "@mui/material": "^7.1.0", "@tailwindcss/vite": "^4.1.7", "clsx": "^2.1.1", "framer-motion": "^12.12.1", "libphonenumber-js": "^1.12.8", "lucide-react": "^0.511.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.56.4", "react-phone-number-input": "^3.4.12", "react-router-dom": "^7.6.0", "react-scroll": "^1.9.3", "react-toastify": "^11.0.5", "react-transition-group": "^4.4.5", "recharts": "^3.0.0", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.7", "zod": "^3.24.4"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/node": "^24.0.12", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.27.0", "eslint-config-prettier": "^10.1.5", "eslint-import-resolver-typescript": "^4.3.5", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.0.0", "postcss": "^8.5.3", "prettier": "^3.5.3", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}