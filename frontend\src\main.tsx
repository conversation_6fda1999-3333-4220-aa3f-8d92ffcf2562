import React from 'react';
import ReactDOM from 'react-dom/client';
import { <PERSON><PERSON><PERSON><PERSON>outer } from 'react-router-dom';
import { CssBaseline } from '@mui/material';
import { MuiWrapper } from './components/common/MuiWrapper';
import { AuthProvider } from './context/AuthContext';
import { ThemeProvider } from './context/ThemeProvider';
import App from './App';
import './assets/styles/globals.css';

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <BrowserRouter>
      <MuiWrapper>
        <CssBaseline />
        <AuthProvider>
          <ThemeProvider>
            <App />
          </ThemeProvider>
        </AuthProvider>
      </MuiWrapper>
    </BrowserRouter>
  </React.StrictMode>
);