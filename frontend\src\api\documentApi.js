import apiClient from './apiClient';

const documentApi = {
  // ==================== DOCUMENT ENDPOINTS (RÉELS SEULEMENT) ====================
  
  // Upload un document - ENDPOINT RÉEL: POST /documents/upload
  uploadDocument: async (file, keycloakId, documentType) => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('keycloakId', keycloakId);
    formData.append('documentType', documentType);

    const response = await apiClient.post('/documents/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  // Lister tous les documents d'un utilisateur - ENDPOINT RÉEL: GET /documents/user/{keycloakId}
  getUserDocuments: async (keycloakId) => {
    const response = await apiClient.get(`/documents/user/${keycloakId}`);
    return response.data;
  },

  // Obtenir la photo de profil active - ENDPOINT RÉEL: GET /documents/user/{keycloakId}/profile-photo
  getActiveProfilePhoto: async (keycloakId) => {
    const response = await apiClient.get(`/documents/user/${keycloakId}/profile-photo`);
    return response.data;
  },

  // Récupérer les métadonnées d'un document - ENDPOINT RÉEL: GET /documents/{documentId}
  getDocumentById: async (documentId) => {
    const response = await apiClient.get(`/documents/${documentId}`);
    return response.data;
  },

  // Visualiser un document - ENDPOINT RÉEL: GET /documents/{documentId}/view
  viewDocument: async (documentId) => {
    const response = await apiClient.get(`/documents/${documentId}/view`, {
      responseType: 'blob'
    });
    return response.data;
  },

  // Télécharger un document - ENDPOINT RÉEL: GET /documents/{documentId}/download
  downloadDocument: async (documentId) => {
    const response = await apiClient.get(`/documents/${documentId}/download`, {
      responseType: 'blob'
    });
    return response.data;
  },

  // Supprimer un document - ENDPOINT RÉEL: DELETE /documents/{documentId}
  deleteDocument: async (documentId) => {
    const response = await apiClient.delete(`/documents/${documentId}`);
    return response.data;
  },
};

export default documentApi;
