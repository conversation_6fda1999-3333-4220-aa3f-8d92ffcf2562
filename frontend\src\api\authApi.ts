// DEPRECATED: Ce fichier est remplacé par les hooks RTK Query
// Utilisez les hooks suivants à la place:
// - useAuth() pour l'authentification
// - useLoginMutation, useLogoutMutation, etc. depuis store/api/authApi.ts

import { LoginCredentials, TokenValidationResult } from '../types/auth';

console.warn('DEPRECATED: authApi.ts is deprecated. Use RTK Query hooks instead.');

// Types pour la compatibilité
interface AuthApiResponse<T = any> {
  data: T;
  message?: string;
}

interface ChangePasswordData {
  currentPassword: string;
  newPassword: string;
}

interface ResetPasswordData {
  token: string;
  newPassword: string;
}

// Interface vide pour la compatibilité
const authApi = {
  login: async (credentials: LoginCredentials): Promise<AuthApiResponse> => {
    throw new Error('DEPRECATED: Use useLoginMutation hook instead');
  },

  logout: async (): Promise<AuthApiResponse> => {
    throw new Error('DEPRECATED: Use useLogoutMutation hook instead');
  },

  refresh: async (): Promise<AuthApiResponse> => {
    throw new Error('DEPRECATED: Use useRefreshSessionMutation hook instead');
  },

  getCurrentUser: async (): Promise<AuthApiResponse<TokenValidationResult>> => {
    throw new Error('DEPRECATED: Use useGetCurrentUserQuery hook instead');
  },

  changePassword: async (passwordData: ChangePasswordData): Promise<AuthApiResponse> => {
    throw new Error('DEPRECATED: Use useChangePasswordMutation hook instead');
  },

  forgotPassword: async (email: string): Promise<AuthApiResponse> => {
    throw new Error('DEPRECATED: Use useForgotPasswordMutation hook instead');
  },

  resetPassword: async (resetData: ResetPasswordData): Promise<AuthApiResponse> => {
    throw new Error('DEPRECATED: Use useResetPasswordMutation hook instead');
  },

  validateSession: async (): Promise<AuthApiResponse<TokenValidationResult>> => {
    throw new Error('DEPRECATED: Use useValidateSessionQuery hook instead');
  },
};

export default authApi;
