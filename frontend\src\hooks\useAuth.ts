import { useSelector, useDispatch } from 'react-redux';
import { useEffect } from 'react';
import {
  useLoginMutation,
  useLogoutMutation,
  useValidateSessionQuery,
  useGetCurrentUserQuery,
} from '../store/api/authApi';
import {
  selectAuth,
  selectIsAuthenticated,
  selectUser,
  selectAuthLoading,
  selectAuthError,
  selectSessionChecked,
  clearError,
  resetAuth,
} from '../store/slices/authSlice';
import { LoginCredentials, TokenValidationResult, UserRole } from '../types/auth';
import type { AppDispatch } from '../store/store';

// Interface pour le hook useAuth
interface UseAuthReturn {
  // État
  isAuthenticated: boolean;
  user: TokenValidationResult | null;
  isLoading: boolean;
  error: string | null;
  sessionChecked: boolean;
  
  // Actions
  login: (credentials: LoginCredentials) => Promise<any>;
  logout: () => Promise<void>;
  checkSession: () => Promise<void>;
  clearError: () => void;
  getRedirectPath: (userRole?: string) => string;
  
  // Utilitaires
  refetchUser: () => void;
  refetchSession: () => void;
  hasRole: (role: UserRole) => boolean;
  hasAnyRole: (roles: UserRole[]) => boolean;
}

// Hook personnalisé pour l'authentification
export const useAuth = (): UseAuthReturn => {
  const dispatch = useDispatch<AppDispatch>();
  
  // Sélecteurs Redux
  const auth = useSelector(selectAuth);
  const isAuthenticated = useSelector(selectIsAuthenticated);
  const user = useSelector(selectUser);
  const isLoading = useSelector(selectAuthLoading);
  const error = useSelector(selectAuthError);
  const sessionChecked = useSelector(selectSessionChecked);

  // Mutations RTK Query
  const [loginMutation, { isLoading: isLoginLoading }] = useLoginMutation();
  const [logoutMutation, { isLoading: isLogoutLoading }] = useLogoutMutation();

  // Queries RTK Query
  const {
    data: sessionData,
    isLoading: isValidatingSession,
    refetch: refetchSession,
  } = useValidateSessionQuery(undefined, {
    skip: sessionChecked, // Skip si déjà vérifié
  });

  const {
    data: currentUserData,
    isLoading: isLoadingUser,
    refetch: refetchUser,
  } = useGetCurrentUserQuery(undefined, {
    skip: !isAuthenticated, // Skip si pas authentifié
  });

  // Utiliser validateSession pour obtenir les données utilisateur (c'est ce qui met à jour Redux)
  const {
    data: validationData,
    isLoading: isValidatingUser,
    refetch: refetchValidation,
  } = useValidateSessionQuery(undefined, {
    skip: false, // Toujours actif - c'est ce qui met à jour l'état Redux
  });

  // Fonction de login
  const login = async (credentials: LoginCredentials) => {
    try {
      console.log('🔐 Starting login process...');
      const result = await loginMutation(credentials).unwrap();
      console.log('✅ Login mutation successful:', result);

      // Après login réussi, récupérer les données utilisateur via /auth/me
      console.log('📡 Fetching user data from /auth/me...');
      const userResult = await refetchUser();
      console.log('✅ User data from /auth/me:', userResult);

      // Vérifier l'état Redux après le login
      console.log('🔍 Redux state after login:', {
        isAuthenticated,
        user,
        isLoading,
        validationData,
        currentUserData
      });

      return result;
    } catch (error) {
      console.error('❌ Login failed:', error);
      throw error;
    }
  };

  // Fonction de logout
  const logout = async () => {
    try {
      await logoutMutation().unwrap();
      dispatch(resetAuth());
    } catch (error) {
      // Même en cas d'erreur, on déconnecte localement
      dispatch(resetAuth());
      throw error;
    }
  };

  // Fonction pour vérifier la session
  const checkSession = async () => {
    try {
      await refetchSession();
    } catch (error) {
      console.error('Session check failed:', error);
    }
  };

  // Fonction pour nettoyer les erreurs
  const clearAuthError = () => {
    dispatch(clearError());
  };

  // Fonction pour obtenir le chemin de redirection selon le rôle
  const getRedirectPath = (userRole?: string): string => {
    const role = userRole || user?.roles?.[0];
    switch (role) {
      case 'CANDIDATE':
        return '/candidate/dashboard';
      case 'RH_ADMIN':
        return '/rh/dashboard';
      case 'PROJECT_LEADER':
        return '/project-leader/dashboard';
      case 'CEO':
        return '/ceo/dashboard';
      default:
        return '/';
    }
  };

  // Fonction pour vérifier si l'utilisateur a un rôle spécifique
  const hasRole = (role: UserRole): boolean => {
    return user?.roles?.includes(role) || false;
  };

  // Fonction pour vérifier si l'utilisateur a au moins un des rôles
  const hasAnyRole = (roles: UserRole[]): boolean => {
    return roles.some(role => hasRole(role));
  };

  // Effet pour initialiser la session au démarrage
  useEffect(() => {
    if (!sessionChecked && !isValidatingSession) {
      checkSession();
    }
  }, [sessionChecked, isValidatingSession]);

  return {
    // État
    isAuthenticated,
    user: currentUserData || user,
    isLoading: isLoading || isLoginLoading || isLogoutLoading || isLoadingUser,
    error,
    sessionChecked,
    
    // Actions
    login,
    logout,
    checkSession,
    clearError: clearAuthError,
    getRedirectPath,
    
    // Utilitaires
    refetchUser,
    refetchSession,
    hasRole,
    hasAnyRole,
  };
};
