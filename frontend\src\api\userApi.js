import apiClient from './apiClient';

const userApi = {
  // ==================== PROFILE ENDPOINTS (RÉELS) ====================

  // Obtenir le profil de l'utilisateur connecté - ENDPOINT RÉEL: GET /profile/me
  getCurrentUserProfile: async () => {
    const response = await apiClient.get('/profile/me');
    return response.data;
  },

  // Mettre à jour le profil de l'utilisateur connecté - ENDPOINT RÉEL: PUT /profile/me
  updateCurrentUserProfile: async (profileData) => {
    const response = await apiClient.put('/profile/me', profileData);
    return response.data;
  },

  // ==================== CANDIDATE ENDPOINTS (RÉELS) ====================

  // Inscription d'un candidat - ENDPOINT RÉEL: POST /users/register
  registerCandidate: async (candidateData) => {
    const response = await apiClient.post('/users/register', candidateData, {
      headers: {
        'Content-Type': undefined, // Laisser Axios définir automatiquement pour FormData
      },
    });
    return response.data;
  },

  // Obtenir tous les candidats - ENDPOINT RÉEL: GET /candidates
  getAllCandidates: async (params = {}) => {
    const response = await apiClient.get('/candidates', { params });
    return response.data;
  },

  // Obtenir un candidat par ID - ENDPOINT RÉEL: GET /candidates/{id}
  getCandidateById: async (id) => {
    const response = await apiClient.get(`/candidates/${id}`);
    return response.data;
  },

  // Mettre à jour un candidat - ENDPOINT RÉEL: PUT /candidates/{id}
  updateCandidate: async (id, candidateData) => {
    const response = await apiClient.put(`/candidates/${id}`, candidateData);
    return response.data;
  },

  // Supprimer un candidat - ENDPOINT RÉEL: DELETE /candidates/{id}
  deleteCandidate: async (id) => {
    const response = await apiClient.delete(`/candidates/${id}`);
    return response.data;
  },

  // ==================== ADMIN USER MANAGEMENT (RÉELS) ====================

  // Obtenir tous les utilisateurs - ENDPOINT RÉEL: GET /users/admin
  getAllUsers: async (params = {}) => {
    const response = await apiClient.get('/users/admin', { params });
    return response.data;
  },

  // Obtenir un utilisateur par ID - ENDPOINT RÉEL: GET /users/admin/{id}
  getUserById: async (id) => {
    const response = await apiClient.get(`/users/admin/${id}`);
    return response.data;
  },

  // Créer un nouvel utilisateur - ENDPOINT RÉEL: POST /users/admin
  createUser: async (userData) => {
    const response = await apiClient.post('/users/admin', userData);
    return response.data;
  },

  // Mettre à jour un utilisateur - ENDPOINT RÉEL: PUT /users/admin/{id}
  updateUser: async (id, userData) => {
    const response = await apiClient.put(`/users/admin/${id}`, userData);
    return response.data;
  },

  // Supprimer un utilisateur - ENDPOINT RÉEL: DELETE /users/admin/{id}
  deleteUser: async (id) => {
    const response = await apiClient.delete(`/users/admin/${id}`);
    return response.data;
  },
};

export default userApi;
