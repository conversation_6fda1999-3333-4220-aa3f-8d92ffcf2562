// ============================================================================
// AUTH SERVICE - Mock Authentication Service
// ============================================================================

export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: 'CANDIDATE' | 'RH' | 'PROJECT_LEADER' | 'CEO';
  avatar?: string;
  isActive: boolean;
  createdAt: string;
  lastLoginAt?: string;
}

// Mock users for testing
const MOCK_USERS: User[] = [
  {
    id: 'user-001',
    email: '<EMAIL>',
    firstName: 'Sara',
    lastName: 'Bouaziz',
    role: 'CANDIDATE',
    avatar: '/avatars/sara.jpg',
    isActive: true,
    createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
    lastLoginAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
  },
  {
    id: 'user-002',
    email: '<EMAIL>',
    firstName: 'Fatima',
    lastName: 'Mansouri',
    role: 'RH',
    avatar: '/avatars/fatima.jpg',
    isActive: true,
    createdAt: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString(),
    lastLoginAt: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString()
  },
  {
    id: 'user-003',
    email: '<EMAIL>',
    firstName: 'Ahmed',
    lastName: 'Ben Salem',
    role: 'PROJECT_LEADER',
    avatar: '/avatars/ahmed.jpg',
    isActive: true,
    createdAt: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString(),
    lastLoginAt: new Date(Date.now() - 30 * 60 * 60 * 1000).toISOString()
  },
  {
    id: 'user-004',
    email: '<EMAIL>',
    firstName: 'Leila',
    lastName: 'Trabelsi',
    role: 'CEO',
    avatar: '/avatars/leila.jpg',
    isActive: true,
    createdAt: new Date(Date.now() - 120 * 24 * 60 * 60 * 1000).toISOString(),
    lastLoginAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString()
  }
];

class AuthService {
  async login(email: string, password: string): Promise<User> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Find user by email
    const user = MOCK_USERS.find(u => u.email.toLowerCase() === email.toLowerCase());
    
    if (!user) {
      throw new Error('Invalid email or password');
    }

    // In a real app, you'd verify the password here
    // For demo purposes, we accept any password

    // Update last login
    const updatedUser = {
      ...user,
      lastLoginAt: new Date().toISOString()
    };

    console.log('🔐 User logged in successfully:', updatedUser.email);
    return updatedUser;
  }

  async logout(): Promise<void> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    console.log('🔐 User logged out successfully');
  }

  async getCurrentUser(): Promise<User | null> {
    try {
      const stored = localStorage.getItem('auth_user');
      return stored ? JSON.parse(stored) : null;
    } catch (error) {
      console.error('Error getting current user:', error);
      return null;
    }
  }

  async register(userData: {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    role: User['role'];
  }): Promise<User> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1200));

    // Check if user already exists
    const existingUser = MOCK_USERS.find(u => u.email.toLowerCase() === userData.email.toLowerCase());
    if (existingUser) {
      throw new Error('User with this email already exists');
    }

    // Create new user
    const newUser: User = {
      id: `user-${Date.now()}`,
      email: userData.email,
      firstName: userData.firstName,
      lastName: userData.lastName,
      role: userData.role,
      isActive: true,
      createdAt: new Date().toISOString(),
      lastLoginAt: new Date().toISOString()
    };

    // Add to mock users (in real app, this would be sent to backend)
    MOCK_USERS.push(newUser);

    console.log('🔐 User registered successfully:', newUser.email);
    return newUser;
  }

  getRedirectPath(role: string): string {
    switch (role) {
      case 'CANDIDATE':
        return '/candidate/dashboard';
      case 'RH':
        return '/rh/dashboard';
      case 'PROJECT_LEADER':
        return '/project-leader/dashboard';
      case 'CEO':
        return '/ceo/dashboard';
      default:
        return '/';
    }
  }
}

export const authService = new AuthService();
export { MOCK_USERS };
