import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';

// Configuration de base pour les appels API
const baseQuery = fetchBaseQuery({
  baseUrl: 'http://localhost:7000/api',
  credentials: 'include', // Important pour les cookies
  prepareHeaders: (headers) => {
    headers.set('Content-Type', 'application/json');
    return headers;
  },
});

// API d'authentification avec RTK Query
export const authApi = createApi({
  reducerPath: 'authApi',
  baseQuery,
  tagTypes: ['Auth', 'User'],
  endpoints: (builder) => ({
    // Login - POST /auth/login
    login: builder.mutation({
      query: (credentials) => ({
        url: '/auth/login',
        method: 'POST',
        body: credentials,
      }),
      invalidatesTags: ['Auth', 'User'],
    }),

    // Logout - POST /auth/logout
    logout: builder.mutation({
      query: () => ({
        url: '/auth/logout',
        method: 'POST',
      }),
      invalidatesTags: ['Auth', 'User'],
    }),

    // Valider la session - POST /auth/validate
    validateSession: builder.query({
      query: () => ({
        url: '/auth/validate',
        method: 'POST',
      }),
      providesTags: ['Auth'],
    }),

    // Rafraîchir la session - POST /auth/refresh
    refreshSession: builder.mutation({
      query: () => ({
        url: '/auth/refresh',
        method: 'POST',
      }),
      invalidatesTags: ['Auth'],
    }),

    // Obtenir les infos de l'utilisateur connecté - GET /auth/me
    getCurrentUser: builder.query({
      query: () => '/auth/me',
      providesTags: ['User'],
    }),

    // Changer le mot de passe - POST /auth/change-password
    changePassword: builder.mutation({
      query: (passwordData) => ({
        url: '/auth/change-password',
        method: 'POST',
        body: passwordData,
      }),
    }),

    // Mot de passe oublié - POST /auth/forgot-password/request
    forgotPassword: builder.mutation({
      query: (email) => ({
        url: '/auth/forgot-password/request',
        method: 'POST',
        body: { email },
      }),
    }),

    // Réinitialiser le mot de passe - POST /auth/forgot-password/reset
    resetPassword: builder.mutation({
      query: (resetData) => ({
        url: '/auth/forgot-password/reset',
        method: 'POST',
        body: resetData,
      }),
    }),
  }),
});

// Export des hooks générés automatiquement
export const {
  useLoginMutation,
  useLogoutMutation,
  useValidateSessionQuery,
  useRefreshSessionMutation,
  useGetCurrentUserQuery,
  useChangePasswordMutation,
  useForgotPasswordMutation,
  useResetPasswordMutation,
} = authApi;
