import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import authApi from '../api/authApi';
import { retryWithBackoff, requestCache } from '../utils/debounce';
import { fetchCurrentUser } from './userSlice';

// État initial
const initialState = {
  isAuthenticated: false,
  isLoading: false,
  isValidating: false, // Pour éviter les validations multiples
  error: null,
  loginTime: null,
  sessionChecked: false, // Important pour vérifier si on a déjà validé la session
};

// Thunks asynchrones
export const login = createAsyncThunk(
  'auth/login',
  async (credentials, { dispatch, rejectWithValue }) => {
    try {
      const response = await authApi.login(credentials);
      // Après login réussi, récupérer immédiatement les infos utilisateur
      await dispatch(fetchCurrentUser());
      return response;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Login failed');
    }
  }
);

export const logout = createAsyncThunk(
  'auth/logout',
  async (_, { rejectWithValue }) => {
    try {
      await authApi.logout();
      return true;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Logout failed');
    }
  }
);

export const validateSession = createAsyncThunk(
  'auth/validateSession',
  async (_, { rejectWithValue, getState }) => {
    try {
      // Vérifier si une validation est déjà en cours
      const { auth } = getState();
      if (auth.isValidating) {
        return rejectWithValue('Validation déjà en cours');
      }

      // Utiliser le cache pour éviter les appels répétés
      const cacheKey = 'validate-session';
      const cached = requestCache.get(cacheKey);
      if (cached) {
        return cached;
      }

      const response = await retryWithBackoff(() => authApi.validateSession());
      requestCache.set(cacheKey, response);
      return response;
    } catch (error) {
      console.error('Erreur validation session:', error);
      return rejectWithValue('Session invalid');
    }
  }
);

// Slice Redux
const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setSessionChecked: (state, action) => {
      state.sessionChecked = action.payload;
    },
  },
  extraReducers: (builder) => {
    // Login
    builder
      .addCase(login.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(login.fulfilled, (state) => {
        state.isLoading = false;
        state.isAuthenticated = true;
        state.loginTime = new Date().toISOString();
        state.lastActivity = new Date().toISOString();
        state.sessionChecked = true;
        state.error = null;

        // L'API Gateway gère les cookies d'authentification automatiquement
        // Pas besoin de stocker des tokens côté frontend
      })
      .addCase(login.rejected, (state, action) => {
        state.isLoading = false;
        state.isAuthenticated = false;
        state.error = action.payload;
      });

    // Logout
    builder
      .addCase(logout.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(logout.fulfilled, (state) => {
        state.isLoading = false;
        state.isAuthenticated = false;
        state.loginTime = null;
        state.lastActivity = null;
        state.sessionChecked = false;
        state.error = null;

        // L'API Gateway gère la suppression des cookies automatiquement
        // Pas besoin de nettoyer manuellement côté frontend
      })
      .addCase(logout.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      });

    // Validate Session
    builder
      .addCase(validateSession.pending, (state) => {
        state.isLoading = true;
        state.isValidating = true;
        state.error = null;
      })
      .addCase(validateSession.fulfilled, (state, action) => {
        console.log('Session validation result:', action.payload);
        state.isLoading = false;
        state.isValidating = false;
        state.sessionChecked = true;

        if (action.payload.valid) {
          state.isAuthenticated = true;
          state.lastActivity = new Date().toISOString();
          state.error = null;
        } else {
          // Session expirée ou invalide - réinitialiser l'état
          state.isAuthenticated = false;
          state.loginTime = null;
          state.lastActivity = null;
        }
      })
      .addCase(validateSession.rejected, (state) => {
        state.isLoading = false;
        state.isValidating = false;
        state.isAuthenticated = false;
        state.sessionChecked = true;
      });
  },
});

export const { clearError, setSessionChecked } = authSlice.actions;
export default authSlice.reducer;