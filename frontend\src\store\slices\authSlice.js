import { createSlice } from '@reduxjs/toolkit';
import { authApi } from '../api/authApi';

// État initial
const initialState = {
  isAuthenticated: false,
  user: null,
  isLoading: false,
  error: null,
  sessionChecked: false,
};

// Slice d'authentification
const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    // Actions synchrones
    clearError: (state) => {
      state.error = null;
    },
    setSessionChecked: (state, action) => {
      state.sessionChecked = action.payload;
    },
    resetAuth: (state) => {
      state.isAuthenticated = false;
      state.user = null;
      state.error = null;
      state.sessionChecked = false;
    },
  },
  extraReducers: (builder) => {
    // Login
    builder
      .addMatcher(authApi.endpoints.login.matchPending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addMatcher(authApi.endpoints.login.matchFulfilled, (state, action) => {
        state.isLoading = false;
        state.isAuthenticated = true;
        state.error = null;
        state.sessionChecked = true;
        // Les données utilisateur seront récupérées séparément
      })
      .addMatcher(authApi.endpoints.login.matchRejected, (state, action) => {
        state.isLoading = false;
        state.isAuthenticated = false;
        state.error = action.error?.message || 'Login failed';
      });

    // Logout
    builder
      .addMatcher(authApi.endpoints.logout.matchPending, (state) => {
        state.isLoading = true;
      })
      .addMatcher(authApi.endpoints.logout.matchFulfilled, (state) => {
        state.isLoading = false;
        state.isAuthenticated = false;
        state.user = null;
        state.error = null;
        state.sessionChecked = false;
      })
      .addMatcher(authApi.endpoints.logout.matchRejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error?.message || 'Logout failed';
      });

    // Validation de session
    builder
      .addMatcher(authApi.endpoints.validateSession.matchPending, (state) => {
        state.isLoading = true;
      })
      .addMatcher(authApi.endpoints.validateSession.matchFulfilled, (state, action) => {
        state.isLoading = false;
        state.sessionChecked = true;
        if (action.payload?.valid) {
          state.isAuthenticated = true;
          state.error = null;
        } else {
          state.isAuthenticated = false;
          state.user = null;
        }
      })
      .addMatcher(authApi.endpoints.validateSession.matchRejected, (state) => {
        state.isLoading = false;
        state.isAuthenticated = false;
        state.user = null;
        state.sessionChecked = true;
      });

    // Obtenir l'utilisateur connecté
    builder
      .addMatcher(authApi.endpoints.getCurrentUser.matchFulfilled, (state, action) => {
        if (action.payload?.valid) {
          state.user = action.payload;
          state.isAuthenticated = true;
        }
      })
      .addMatcher(authApi.endpoints.getCurrentUser.matchRejected, (state) => {
        state.user = null;
        state.isAuthenticated = false;
      });
  },
});

// Export des actions
export const { clearError, setSessionChecked, resetAuth } = authSlice.actions;

// Sélecteurs
export const selectAuth = (state) => state.auth;
export const selectIsAuthenticated = (state) => state.auth.isAuthenticated;
export const selectUser = (state) => state.auth.user;
export const selectAuthLoading = (state) => state.auth.isLoading;
export const selectAuthError = (state) => state.auth.error;
export const selectSessionChecked = (state) => state.auth.sessionChecked;

export default authSlice.reducer;
