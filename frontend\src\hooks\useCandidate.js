import { useRegisterCandidateMutation } from '../store/api/userApi';

// Hook personnalisé pour les opérations candidat
export const useCandidate = () => {
  const [registerCandidateMutation, { isLoading: isRegistering, error: registerError }] = useRegisterCandidateMutation();

  // Fonction d'inscription candidat
  const registerCandidate = async (candidateData) => {
    try {
      // Créer FormData pour l'upload de fichier
      const formData = new FormData();
      
      // Ajouter tous les champs au FormData
      formData.append('firstName', candidateData.firstName);
      formData.append('lastName', candidateData.lastName);
      formData.append('email', candidateData.email);
      formData.append('password', candidateData.password);
      formData.append('phone', candidateData.phone);
      formData.append('location', candidateData.location || '');
      formData.append('linkedinUrl', candidateData.linkedinUrl || '');
      formData.append('portfolioUrl', candidateData.portfolioUrl || '');
      formData.append('dateOfBirth', candidateData.dateOfBirth || '');
      
      // Gérer les catégories préférées
      if (candidateData.preferredCategories && candidateData.preferredCategories.length > 0) {
        formData.append('preferredCategories', candidateData.preferredCategories.join(','));
      }
      
      // Ajouter le fichier CV
      if (candidateData.cvFile) {
        formData.append('cvFile', candidateData.cvFile);
      }

      const result = await registerCandidateMutation(formData).unwrap();
      return result;
    } catch (error) {
      throw error;
    }
  };

  return {
    registerCandidate,
    isRegistering,
    registerError,
  };
};
