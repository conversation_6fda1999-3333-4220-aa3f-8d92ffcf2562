// ============================================================================
// REDUX STORE CONFIGURATION
// ============================================================================

import { configureStore } from '@reduxjs/toolkit';
import candidateSlice from './slices/candidateSlice';
import uiSlice from './slices/uiSlice';

export const store = configureStore({
  reducer: {
    candidate: candidateSlice,
    ui: uiSlice,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
      },
    }),
  devTools: process.env.NODE_ENV !== 'production',
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
