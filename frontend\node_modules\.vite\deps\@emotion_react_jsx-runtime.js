import {
  require_jsx_runtime
} from "./chunk-NJLIVH7H.js";
import {
  Emotion$1,
  createEmotionProps,
  hasOwn,
  require_hoist_non_react_statics_cjs
} from "./chunk-X56U2HY2.js";
import "./chunk-4JLRNKH6.js";
import {
  require_react
} from "./chunk-HUL2CLQT.js";
import {
  __toESM
} from "./chunk-EWTE5DHJ.js";

// node_modules/@emotion/react/jsx-runtime/dist/emotion-react-jsx-runtime.browser.development.esm.js
var ReactJSXRuntime = __toESM(require_jsx_runtime());
var import_react = __toESM(require_react());
var import_hoist_non_react_statics = __toESM(require_hoist_non_react_statics_cjs());
var Fragment2 = ReactJSXRuntime.Fragment;
var jsx2 = function jsx3(type, props, key) {
  if (!hasOwn.call(props, "css")) {
    return ReactJSXRuntime.jsx(type, props, key);
  }
  return ReactJSXRuntime.jsx(Emotion$1, createEmotionProps(type, props), key);
};
var jsxs2 = function jsxs3(type, props, key) {
  if (!hasOwn.call(props, "css")) {
    return ReactJSXRuntime.jsxs(type, props, key);
  }
  return ReactJSXRuntime.jsxs(Emotion$1, createEmotionProps(type, props), key);
};
export {
  Fragment2 as Fragment,
  jsx2 as jsx,
  jsxs2 as jsxs
};
//# sourceMappingURL=@emotion_react_jsx-runtime.js.map
