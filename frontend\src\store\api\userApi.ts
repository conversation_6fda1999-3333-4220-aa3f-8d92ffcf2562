import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { User, UserProfile, CandidateRegistrationData } from '../../types/auth';

// Interfaces pour les réponses
interface UserRegistrationResponse {
  keycloakId: string;
  email: string;
  firstName: string;
  lastName: string;
  phone: string;
  userType: string;
  message: string;
}

interface CandidateProfileResponse {
  keycloakId: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  location?: string;
  linkedinUrl?: string;
  portfolioUrl?: string;
  dateOfBirth?: string;
  preferredCategories?: string[];
  cvDocumentId?: number;
  profilePhotoUrl?: string;
  createdAt: string;
  updatedAt: string;
}

interface QueryParams {
  page?: number;
  size?: number;
  search?: string;
  [key: string]: any;
}

// Configuration de base pour les appels API
const baseQuery = fetchBaseQuery({
  baseUrl: 'http://localhost:7000/api',
  credentials: 'include', // Important pour les cookies
  prepareHeaders: (headers, { endpoint }) => {
    // Pour les uploads de fichiers, ne pas définir Content-Type
    // Le navigateur le fera automatiquement pour FormData
    if (endpoint !== 'registerCandidate' && endpoint !== 'updateProfilePhoto') {
      headers.set('Content-Type', 'application/json');
    }
    return headers;
  },
});

// API des utilisateurs avec RTK Query
export const userApi = createApi({
  reducerPath: 'userApi',
  baseQuery,
  tagTypes: ['Profile', 'Candidate', 'User'],
  endpoints: (builder) => ({
    // Inscription candidat - POST /users/register
    registerCandidate: builder.mutation<UserRegistrationResponse, FormData>({
      query: (formData) => ({
        url: '/users/register',
        method: 'POST',
        body: formData,
      }),
      invalidatesTags: ['Candidate'],
    }),

    // Obtenir le profil de l'utilisateur connecté - GET /profile/me
    getCurrentUserProfile: builder.query<CandidateProfileResponse | User, void>({
      query: () => '/profile/me',
      providesTags: ['Profile'],
    }),

    // Mettre à jour le profil de l'utilisateur connecté - PUT /profile/me
    updateCurrentUserProfile: builder.mutation<CandidateProfileResponse, Partial<UserProfile>>({
      query: (profileData) => ({
        url: '/profile/me',
        method: 'PUT',
        body: profileData,
      }),
      invalidatesTags: ['Profile'],
    }),

    // Obtenir un candidat par ID Keycloak - GET /profile/candidate/{keycloakId}
    getCandidateProfile: builder.query<CandidateProfileResponse, string>({
      query: (keycloakId) => `/profile/candidate/${keycloakId}`,
      providesTags: (result, error, keycloakId) => [
        { type: 'Candidate', id: keycloakId },
      ],
    }),

    // Mettre à jour la photo de profil - PUT /profile/photo
    updateProfilePhoto: builder.mutation<{ message: string; photoUrl: string }, FormData>({
      query: (formData) => ({
        url: '/profile/photo',
        method: 'PUT',
        body: formData,
      }),
      invalidatesTags: ['Profile'],
    }),

    // Obtenir tous les candidats (pour les admins) - GET /candidates
    getAllCandidates: builder.query<CandidateProfileResponse[], QueryParams>({
      query: (params = {}) => ({
        url: '/candidates',
        params,
      }),
      providesTags: ['Candidate'],
    }),

    // Compter les profils candidats - GET /profile/candidates/count
    getCandidatesCount: builder.query<{ count: number }, void>({
      query: () => '/profile/candidates/count',
      providesTags: ['Candidate'],
    }),

    // Obtenir un candidat par ID Keycloak (alternative) - GET /profile/candidates/{keycloakId}
    getCandidateByKeycloakId: builder.query<CandidateProfileResponse, string>({
      query: (keycloakId) => `/profile/candidates/${keycloakId}`,
      providesTags: (result, error, keycloakId) => [
        { type: 'Candidate', id: keycloakId },
      ],
    }),

    // Mettre à jour un candidat (pour les admins) - PUT /candidates/{keycloakId}
    updateCandidate: builder.mutation<CandidateProfileResponse, { keycloakId: string; data: Partial<CandidateProfileResponse> }>({
      query: ({ keycloakId, data }) => ({
        url: `/candidates/${keycloakId}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { keycloakId }) => [
        { type: 'Candidate', id: keycloakId },
        'Candidate',
      ],
    }),

    // Supprimer un candidat (pour les admins) - DELETE /candidates/{keycloakId}
    deleteCandidate: builder.mutation<{ message: string }, string>({
      query: (keycloakId) => ({
        url: `/candidates/${keycloakId}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Candidate'],
    }),
  }),
});

// Export des hooks générés automatiquement
export const {
  useRegisterCandidateMutation,
  useGetCurrentUserProfileQuery,
  useUpdateCurrentUserProfileMutation,
  useGetCandidateProfileQuery,
  useUpdateProfilePhotoMutation,
  useGetAllCandidatesQuery,
  useGetCandidatesCountQuery,
  useGetCandidateByKeycloakIdQuery,
  useUpdateCandidateMutation,
  useDeleteCandidateMutation,
} = userApi;
